# Copyright (c) 2024, Brain<PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe, iam
import parent_ngo
from frappe import _, share
from parent_ngo.utils.validation import is_valid_mobile_number
from frappe.model.document import Document

class CustomerNGO(Document):
	def validate(self):
		self.validate_nids()
		self.validate_mobile_nos()
		self.validate_board_members()

	def before_save(self):
		self.create_user_account()
		self.unshare_with_self()

	def on_update(self):
		self.share_with_self()

	def validate_nids(self):
		# Validates National IDs of Contact Person & Treasurer of Customer NGO
		if len(self.contact_nid) != 14 or not self.contact_nid.isdigit():
			exception = iam.InvalidNIDFieldError(targets="contact_nid")
			iam.throw(_(parent_ngo.ERRORS.get("invalid_contact_nid")), exception=exception)
		if len(self.treasurer_nid) != 14 or not self.treasurer_nid.isdigit():
			exception = iam.InvalidNIDFieldError(targets="treasurer_nid")
			iam.throw(_(parent_ngo.ERRORS.get("invalid_treasurer_nid")), exception=exception)
		# Only validate CEO N<PERSON> if CEO data is provided (skip during onboarding when CEO data is not yet filled)
		if self.ceo_nid and (len(self.ceo_nid) != 14 or not self.ceo_nid.isdigit()):
			exception = iam.InvalidNIDFieldError(targets="ceo_nid")
			iam.throw(_(parent_ngo.ERRORS.get("invalid_ceo_nid")), exception=exception)

	def validate_mobile_nos(self):
		# Validates Mobile Numbers of Contact Person & Treasurer of Customer NGO
		if not is_valid_mobile_number(self.contact_mobile_no):
			exception = iam.InvalidEgyptMobileNumberFieldError(targets="contact_mobile_no")
			iam.throw(_(parent_ngo.ERRORS.get("invalid_contact_mobile_no")), exception=exception)
		if not is_valid_mobile_number(self.treasurer_mobile_no):
			exception = iam.InvalidEgyptMobileNumberFieldError(targets="treasurer_mobile_no")
			iam.throw(_(parent_ngo.ERRORS.get("invalid_treasurer_mobile_no")), exception=exception)
		# Only validate CEO mobile number if CEO data is provided (skip during onboarding when CEO data is not yet filled)
		if self.ceo_mobile_no and not is_valid_mobile_number(self.ceo_mobile_no):
			exception = iam.InvalidEgyptMobileNumberFieldError(targets="ceo_mobile_no")
			iam.throw(_(parent_ngo.ERRORS.get("invalid_ceo_mobile_no")), exception=exception)

	def validate_board_members(self):
		# Validates National IDs & Mobile Numbers of Board Members of Customer NGO
		for member in self.board_members:
			if len(member.nid) != 14 or not member.nid.isdigit():
				exception = iam.InvalidNIDTableFieldError(targets={"target": "nid", "target_table": "board_members"}, row_idx=member.idx)
				iam.throw(_(parent_ngo.ERRORS.get("invalid_board_member_nid")).format(member.idx), exception=exception)
			if not is_valid_mobile_number(member.mobile_no):
				exception = iam.InvalidEgyptMobileNumberTableFieldError(targets={"target": "mobile_no", "target_table": "board_members"}, row_idx=member.idx)
				iam.throw(_(parent_ngo.ERRORS.get("invalid_board_member_mobile_no")).format(member.idx), exception=exception)
	
	def validate_attachments(self):
		# Validates that the uploaded attachments matches the "Customer NGO Attachments"
		# read required_attachments from the "Customer NGO Attachments" single doctype
		customer_ngo_attachments = frappe.get_single("Customer NGO Attachments")
		required_attachments = customer_ngo_attachments.required_attachments
		uploaded_attachments_names = {attachment.name1 for attachment in self.uploaded_attachments if attachment.attachment}
		missing_attachments = [attachment.name1 for attachment in required_attachments if attachment.name1 not in uploaded_attachments_names]
		if missing_attachments:
			exception = parent_ngo.MissingRequiredAttachmentsError(targets={"missing_attachments": ", ".join(missing_attachments)})
			iam.throw(_(parent_ngo.ERRORS.get("missing_required_attachments").format("<br>".join([''] + missing_attachments))), exception=exception)

		
	def create_user_account(self):
		# Creates the user account for the Customer NGO using the contact person email if approved
		if self.status == "Approved" and not self.user:
			user = frappe.new_doc("User")
			user.email = self.contact_email
			user.first_name = self.contact_full_name
			user.mobile_no = self.contact_mobile_no
			user.role_profile_name = "Customer NGO"
			user.save()
			self.user = user.name

	def set_ngo_members_attachments(self, attachments, doc):
		# Sets the NGO Members attachments
		for field, file_url in attachments.items():
			if file_url and self.get(field) != file_url:
				if self.get(field):
					self.delete_attachment(field, self.get(field))
				self.set(field, self.copy_attachment("Loan Request", doc, field, file_url))

	def set_ngo_board_members_attachments(self, board_members, doc):
		# Sets the NGO Board Members attachments
		attachment_fields = ["nid_front", "nid_back", "bm_iscore_report"]
		for member in board_members:
			board_member = self.get_ngo_board_member_details(member.nid)
			self.board_members[board_member.idx - 1].set("bm_iscore_result", member.bm_iscore_result)
			for field in attachment_fields:
				if member.get(field) and board_member.get(field) != member.get(field):
					if board_member.get(field):
						self.delete_attachment(field, board_member.get(field))
					self.board_members[board_member.idx - 1].set(field, self.copy_attachment("Loan Request", doc, field, member.get(field)))

	def get_ngo_board_member_details(self, nid):
		details = frappe.db.get_value("Board Member Details", {
			"parenttype": self.doctype,
			"parent": self.name,
			"parentfield": "board_members",
			"nid": nid
		}, ["idx", "nid_front", "nid_back", "bm_iscore_report"], as_dict=True)
		return details or {}

	def copy_attachment(self, doctype, doc, field, file_url):
		# Gets a copy of an existing file document and creates a new one based on it
		file = frappe.db.get_value("File", {
			"attached_to_doctype": doctype,
			"attached_to_name": doc,
			"attached_to_field": field,
			"file_url": file_url,
		}, "name", order_by="creation desc")
		if file:
			attachment_doc = frappe.copy_doc(frappe.get_doc("File", file))
			attachment_doc.attached_to_doctype = self.doctype
			attachment_doc.attached_to_name = self.name
			attachment_doc.attached_to_field = field
			attachment_doc.save()
			return attachment_doc.file_url
		return None

	def delete_attachment(self, field, file_url=None):
		# Deletes an existing file document using its file_url
		if not file_url:
			return
		file = frappe.db.get_value("File", {
			"attached_to_doctype": self.doctype,
			"attached_to_name": self.name,
			"attached_to_field": field,
			"file_url": file_url,
		}, "name", order_by="creation desc")
		if file:
			frappe.delete_doc("File", file)

	def share_with_self(self):
		# Share the Customer NGO with the User associated with it
		if not self.user:
			return
		share.add_docshare(self.doctype, self.name, self.user, write=1, flags={"ignore_share_permission": True})

	def unshare_with_self(self):
		# Remove share assignment from the old user associated with the Customer NGO
		if not self._doc_before_save:
			return
		old_user = self._doc_before_save.user
		if old_user and not old_user == self.user:
			share.remove(self.doctype, self.name, old_user, flags={"ignore_permissions": True, "ignore_share_permission": True})

	@frappe.whitelist()
	def send_welcome_email(self):
		# Sends a welcome email to the Customer NGO's user
		if not self.user:
			return
		user = frappe.get_doc("User", self.user)
		user.send_welcome_mail_to_user()
		frappe.msgprint(_(parent_ngo.MESSAGES.get("welcome_email_sent")))

	@frappe.whitelist()
	def send_reset_password_email(self):
		# Sends a reset password email to the Customer NGO's user
		if not self.user:
			return
		user = frappe.get_doc("User", self.user)
		user.reset_password(send_email=True)
		frappe.msgprint(_(parent_ngo.MESSAGES.get("reset_password_email_sent")))
