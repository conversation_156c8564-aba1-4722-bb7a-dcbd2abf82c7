2025-06-10 21:54:48,815 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for parentngo
2025-06-10 21:54:48,830 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-10 21:57:52,014 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-10 21:57:52,019 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-10 21:57:52,033 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-10 21:58:53,771 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-10 21:58:53,775 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-10 21:58:53,792 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-10 21:59:54,552 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-10 21:59:54,555 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-10 21:59:54,572 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-10 22:01:57,403 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for parentngo
2025-06-10 22:01:57,406 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for parentngo
2025-06-10 22:01:57,415 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for parentngo
2025-06-10 22:01:57,418 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for parentngo
2025-06-10 22:01:57,420 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for parentngo
2025-06-10 22:01:57,491 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for parentngo
2025-06-10 22:01:57,494 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for parentngo
2025-06-10 22:01:57,497 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for parentngo
2025-06-10 22:01:57,500 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for parentngo
2025-06-10 22:01:57,502 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for parentngo
2025-06-10 22:01:57,509 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for parentngo
2025-06-10 22:01:57,557 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-06-10 22:01:57,559 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for parentngo
2025-06-10 22:01:57,562 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for parentngo
2025-06-10 22:01:57,564 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-06-10 22:01:57,567 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-06-10 22:01:57,570 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-06-10 22:01:57,573 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for parentngo
2025-06-10 22:01:57,584 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for parentngo
2025-06-10 22:01:57,586 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-06-10 22:01:57,589 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-06-10 22:01:57,591 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-06-10 22:02:57,826 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for parentngo
2025-06-10 22:02:57,830 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for parentngo
2025-06-10 22:02:57,841 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for parentngo
2025-06-10 22:02:57,843 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for parentngo
2025-06-10 22:02:57,846 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for parentngo
2025-06-10 22:02:57,862 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-06-10 22:02:57,920 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for parentngo
2025-06-10 22:02:57,923 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for parentngo
2025-06-10 22:02:57,925 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for parentngo
2025-06-10 22:02:57,928 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for parentngo
2025-06-10 22:02:57,930 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for parentngo
2025-06-10 22:02:57,937 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for parentngo
2025-06-10 22:02:57,983 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-06-10 22:02:57,986 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for parentngo
2025-06-10 22:02:57,988 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for parentngo
2025-06-10 22:02:57,991 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-06-10 22:02:57,994 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-06-10 22:02:57,997 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-06-10 22:02:57,999 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for parentngo
2025-06-10 22:02:58,009 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for parentngo
2025-06-10 22:02:58,012 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-06-10 22:02:58,014 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-06-10 22:02:58,017 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-06-10 22:03:59,611 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for parentngo
2025-06-10 22:03:59,614 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for parentngo
2025-06-10 22:03:59,623 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for parentngo
2025-06-10 22:03:59,626 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for parentngo
2025-06-10 22:03:59,628 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for parentngo
2025-06-10 22:03:59,722 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for parentngo
2025-06-10 22:03:59,725 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for parentngo
2025-06-10 22:03:59,728 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for parentngo
2025-06-10 22:03:59,731 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for parentngo
2025-06-10 22:03:59,733 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for parentngo
2025-06-10 22:03:59,741 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for parentngo
2025-06-10 22:03:59,793 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-06-10 22:03:59,795 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for parentngo
2025-06-10 22:03:59,798 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for parentngo
2025-06-10 22:03:59,801 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-06-10 22:03:59,804 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-06-10 22:03:59,807 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-06-10 22:03:59,810 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for parentngo
2025-06-10 22:03:59,821 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for parentngo
2025-06-10 22:03:59,826 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-06-10 22:03:59,831 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-06-10 22:03:59,835 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-06-10 22:05:01,013 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for parentngo
2025-06-10 22:05:01,016 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for parentngo
2025-06-10 22:05:01,019 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-06-10 22:05:01,028 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for parentngo
2025-06-10 22:05:01,032 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for parentngo
2025-06-10 22:05:01,035 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for parentngo
2025-06-10 22:05:01,119 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for parentngo
2025-06-10 22:05:01,122 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for parentngo
2025-06-10 22:05:01,125 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for parentngo
2025-06-10 22:05:01,128 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for parentngo
2025-06-10 22:05:01,131 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for parentngo
2025-06-10 22:05:01,139 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for parentngo
2025-06-10 22:05:01,183 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-06-10 22:05:01,186 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for parentngo
2025-06-10 22:05:01,188 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for parentngo
2025-06-10 22:05:01,191 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-06-10 22:05:01,193 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-06-10 22:05:01,196 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-06-10 22:05:01,198 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for parentngo
2025-06-10 22:05:01,210 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for parentngo
2025-06-10 22:05:01,213 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-06-10 22:05:01,215 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-06-10 22:05:01,218 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-06-10 22:33:33,752 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-10 22:33:33,757 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-10 22:33:33,773 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-10 22:34:34,436 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-10 22:34:34,439 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-10 22:34:34,452 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-10 22:35:36,173 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-10 22:35:36,175 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-10 22:35:36,189 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-10 22:37:37,571 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-10 22:37:37,574 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-10 22:37:37,587 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-10 22:38:38,944 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-10 22:38:38,948 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-10 22:38:38,963 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-10 22:39:40,058 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-10 22:39:40,061 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-10 22:39:40,076 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-10 22:40:41,168 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-10 22:40:41,173 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-10 22:40:41,199 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-10 22:45:46,030 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-10 22:45:46,034 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-10 22:45:46,059 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-10 22:49:49,858 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-10 22:49:49,861 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-10 22:49:49,874 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-10 22:50:50,888 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-10 22:50:50,894 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-10 22:50:50,926 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-15 10:58:02,868 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-06-15 10:58:02,881 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for parentngo
2025-06-15 10:58:02,883 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for parentngo
2025-06-15 10:58:02,888 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for parentngo
2025-06-15 10:58:02,892 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-06-15 10:58:02,896 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-06-15 10:58:02,900 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for parentngo
2025-06-15 10:58:02,903 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for parentngo
2025-06-15 10:58:02,906 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for parentngo
2025-06-15 10:58:02,908 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for parentngo
2025-06-15 10:58:02,912 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for parentngo
2025-06-15 10:58:02,980 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for parentngo
2025-06-15 10:58:02,982 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for parentngo
2025-06-15 10:58:02,985 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for parentngo
2025-06-15 10:58:02,987 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for parentngo
2025-06-15 10:58:02,990 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for parentngo
2025-06-15 10:58:02,992 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for parentngo
2025-06-15 10:58:02,995 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for parentngo
2025-06-15 10:58:02,998 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for parentngo
2025-06-15 10:58:03,000 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-06-15 10:58:03,017 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for parentngo
2025-06-15 10:58:03,020 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for parentngo
2025-06-15 10:58:03,023 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for parentngo
2025-06-15 10:58:03,026 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-06-15 10:58:03,029 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for parentngo
2025-06-15 10:58:03,031 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for parentngo
2025-06-15 10:58:03,035 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-06-15 10:58:03,037 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-06-15 10:58:03,040 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-06-15 10:58:03,048 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for parentngo
2025-06-15 10:58:03,051 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-15 10:58:03,053 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-15 10:58:03,056 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for parentngo
2025-06-15 10:58:03,058 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-06-15 10:58:03,061 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for parentngo
2025-06-15 10:58:03,063 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-06-15 10:58:03,066 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-06-15 10:58:03,068 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-06-15 10:58:03,071 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-15 14:17:58,544 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for parentngo
2025-06-15 16:29:24,266 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-15 16:29:24,272 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-15 16:29:24,303 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-15 16:31:26,942 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for parentngo
2025-06-15 16:31:26,946 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for parentngo
2025-06-15 16:31:26,952 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for parentngo
2025-06-15 16:31:26,954 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for parentngo
2025-06-15 16:31:26,956 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for parentngo
2025-06-15 16:31:27,022 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for parentngo
2025-06-15 16:31:27,026 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for parentngo
2025-06-15 16:31:27,028 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for parentngo
2025-06-15 16:31:27,082 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-06-15 16:31:27,085 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for parentngo
2025-06-15 16:31:27,088 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-06-15 16:31:27,090 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-06-15 16:31:27,092 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-06-15 16:32:28,881 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for parentngo
2025-06-15 16:32:28,885 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for parentngo
2025-06-15 16:32:28,896 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for parentngo
2025-06-15 16:32:28,899 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for parentngo
2025-06-15 16:32:28,903 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for parentngo
2025-06-15 16:32:28,996 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for parentngo
2025-06-15 16:32:29,001 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for parentngo
2025-06-15 16:32:29,003 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for parentngo
2025-06-15 16:32:29,076 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-06-15 16:32:29,079 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for parentngo
2025-06-15 16:32:29,081 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-06-15 16:32:29,084 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-06-15 16:32:29,086 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-06-15 16:33:29,203 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for parentngo
2025-06-15 16:33:29,206 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for parentngo
2025-06-15 16:33:29,213 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for parentngo
2025-06-15 16:33:29,215 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for parentngo
2025-06-15 16:33:29,217 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for parentngo
2025-06-15 16:33:29,282 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for parentngo
2025-06-15 16:33:29,289 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for parentngo
2025-06-15 16:33:29,293 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for parentngo
2025-06-15 16:33:29,354 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-15 16:33:29,356 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-15 16:33:29,361 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-06-15 16:33:29,364 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for parentngo
2025-06-15 16:33:29,367 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-06-15 16:33:29,369 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-06-15 16:33:29,372 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-06-15 16:33:29,374 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-15 16:34:30,382 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for parentngo
2025-06-15 16:34:30,385 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for parentngo
2025-06-15 16:34:30,391 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for parentngo
2025-06-15 16:34:30,394 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for parentngo
2025-06-15 16:34:30,396 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for parentngo
2025-06-15 16:34:30,462 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for parentngo
2025-06-15 16:34:30,469 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for parentngo
2025-06-15 16:34:30,473 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for parentngo
2025-06-15 16:34:30,536 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-15 16:34:30,539 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-15 16:34:30,543 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-06-15 16:34:30,545 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for parentngo
2025-06-15 16:34:30,548 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-06-15 16:34:30,550 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-06-15 16:34:30,553 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-06-15 16:34:30,555 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-15 16:37:34,784 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-15 16:37:34,786 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-15 16:37:34,799 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-15 16:38:35,656 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-15 16:38:35,658 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-15 16:38:35,672 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-15 16:39:36,074 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-15 16:39:36,076 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-15 16:39:36,089 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-16 10:57:56,321 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for parentngo
2025-06-16 10:57:56,328 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for parentngo
2025-06-16 10:57:56,332 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-06-16 10:57:56,342 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for parentngo
2025-06-16 10:57:56,347 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for parentngo
2025-06-16 10:57:56,351 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for parentngo
2025-06-16 10:57:56,355 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for parentngo
2025-06-16 10:57:56,364 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for parentngo
2025-06-16 10:57:56,368 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for parentngo
2025-06-16 10:57:56,375 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-06-16 10:57:56,378 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-06-16 10:57:56,384 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for parentngo
2025-06-16 10:57:56,388 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for parentngo
2025-06-16 10:57:56,392 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for parentngo
2025-06-16 10:57:56,397 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for parentngo
2025-06-16 10:57:56,401 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for parentngo
2025-06-16 10:57:56,404 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for parentngo
2025-06-16 10:57:56,407 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for parentngo
2025-06-16 10:57:56,410 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for parentngo
2025-06-16 10:57:56,414 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for parentngo
2025-06-16 10:57:56,417 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for parentngo
2025-06-16 10:57:56,420 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for parentngo
2025-06-16 10:57:56,424 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for parentngo
2025-06-16 10:57:56,427 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for parentngo
2025-06-16 10:57:56,430 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for parentngo
2025-06-16 10:57:56,433 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for parentngo
2025-06-16 10:57:56,437 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for parentngo
2025-06-16 10:57:56,441 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for parentngo
2025-06-16 10:57:56,444 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for parentngo
2025-06-16 10:57:56,447 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for parentngo
2025-06-16 10:57:56,450 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for parentngo
2025-06-16 10:57:56,454 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for parentngo
2025-06-16 10:57:56,458 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for parentngo
2025-06-16 10:57:56,462 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for parentngo
2025-06-16 10:57:56,465 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for parentngo
2025-06-16 10:57:56,469 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for parentngo
2025-06-16 10:57:56,473 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for parentngo
2025-06-16 10:57:56,477 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for parentngo
2025-06-16 10:57:56,480 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for parentngo
2025-06-16 10:57:56,483 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for parentngo
2025-06-16 10:57:56,487 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for parentngo
2025-06-16 10:57:56,490 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for parentngo
2025-06-16 10:57:56,493 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for parentngo
2025-06-16 10:57:56,496 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for parentngo
2025-06-16 10:57:56,499 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for parentngo
2025-06-16 10:57:56,533 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for parentngo
2025-06-16 10:57:56,538 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for parentngo
2025-06-16 10:57:56,544 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for parentngo
2025-06-16 10:57:56,549 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-06-16 10:57:56,555 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.set_auto_repeat_as_completed because it was found in queue for parentngo
2025-06-16 10:57:56,562 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for parentngo
2025-06-16 10:57:56,570 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for parentngo
2025-06-16 10:57:56,576 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for parentngo
2025-06-16 10:57:56,582 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for parentngo
2025-06-16 10:57:56,588 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for parentngo
2025-06-16 10:57:56,594 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for parentngo
2025-06-16 10:57:56,601 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for parentngo
2025-06-16 10:57:56,609 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for parentngo
2025-06-16 10:57:56,615 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for parentngo
2025-06-16 10:57:56,622 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-06-16 10:57:56,630 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for parentngo
2025-06-16 10:57:56,636 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for parentngo
2025-06-16 10:57:56,643 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-06-16 10:57:56,649 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-06-16 10:57:56,656 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-06-16 10:57:56,661 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for parentngo
2025-06-16 10:57:56,665 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-16 10:57:56,671 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-16 10:57:56,675 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for parentngo
2025-06-16 10:57:56,680 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-06-16 10:57:56,686 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for parentngo
2025-06-16 10:57:56,693 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-06-16 10:57:56,699 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-06-16 10:57:56,705 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-06-16 10:57:56,714 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-16 10:58:58,387 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-06-16 10:58:58,422 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for parentngo
2025-06-16 10:58:58,431 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-06-16 10:58:58,436 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-06-16 10:58:58,444 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for parentngo
2025-06-16 10:58:58,449 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for parentngo
2025-06-16 10:58:58,452 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for parentngo
2025-06-16 10:58:58,455 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for parentngo
2025-06-16 10:58:58,458 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for parentngo
2025-06-16 10:58:58,566 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for parentngo
2025-06-16 10:58:58,570 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for parentngo
2025-06-16 10:58:58,574 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for parentngo
2025-06-16 10:58:58,577 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-06-22 13:07:09,281 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-06-22 19:14:20,998 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for parentngo
2025-06-22 19:14:21,003 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for parentngo
2025-06-22 19:14:21,008 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-06-22 19:14:21,017 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for parentngo
2025-06-22 19:14:21,021 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for parentngo
2025-06-22 19:14:21,025 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for parentngo
2025-06-22 19:14:21,040 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-06-22 19:14:21,043 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-06-22 19:14:21,128 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for parentngo
2025-06-22 19:14:21,131 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for parentngo
2025-06-22 19:14:21,135 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for parentngo
2025-06-22 19:14:21,138 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for parentngo
2025-06-22 19:14:21,141 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for parentngo
2025-06-22 19:14:21,148 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for parentngo
2025-06-22 19:14:21,152 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for parentngo
2025-06-22 19:14:21,237 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-06-22 19:14:21,241 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for parentngo
2025-06-22 19:14:21,246 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for parentngo
2025-06-22 19:14:21,251 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-06-22 19:14:21,255 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-06-22 19:14:21,260 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-06-22 19:14:21,265 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for parentngo
2025-06-22 19:14:21,271 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-22 19:14:21,276 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-22 19:14:21,284 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-06-22 19:14:21,289 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for parentngo
2025-06-22 19:14:21,294 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-06-22 19:14:21,300 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-06-22 19:14:21,306 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-06-22 19:14:21,312 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-23 13:01:47,732 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-06-23 13:01:47,761 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for parentngo
2025-06-23 13:01:47,772 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-06-23 13:01:47,778 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-06-23 13:01:47,789 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for parentngo
2025-06-23 13:01:47,795 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for parentngo
2025-06-23 13:01:47,801 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for parentngo
2025-06-23 13:01:47,807 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for parentngo
2025-06-23 13:01:47,813 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for parentngo
2025-06-23 13:01:47,981 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for parentngo
2025-06-23 13:01:47,987 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for parentngo
2025-06-23 13:01:47,993 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for parentngo
2025-06-23 13:01:47,998 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-06-24 13:50:01,665 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for parentngo
2025-06-24 13:50:01,668 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for parentngo
2025-06-24 13:50:01,671 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-06-24 13:50:01,678 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for parentngo
2025-06-24 13:50:01,681 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for parentngo
2025-06-24 13:50:01,683 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for parentngo
2025-06-24 13:50:01,698 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-06-24 13:50:01,701 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-06-24 13:50:01,762 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for parentngo
2025-06-24 13:50:01,765 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for parentngo
2025-06-24 13:50:01,768 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for parentngo
2025-06-24 13:50:01,771 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for parentngo
2025-06-24 13:50:01,773 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for parentngo
2025-06-24 13:50:01,778 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for parentngo
2025-06-24 13:50:01,781 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for parentngo
2025-06-24 13:50:01,831 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-06-24 13:50:01,835 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for parentngo
2025-06-24 13:50:01,838 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for parentngo
2025-06-24 13:50:01,840 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-06-24 13:50:01,843 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-06-24 13:50:01,846 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-06-24 13:50:01,849 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for parentngo
2025-06-24 13:50:01,852 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-06-24 13:50:01,856 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-06-24 13:50:01,861 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-06-24 13:50:01,864 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for parentngo
2025-06-24 13:50:01,867 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-06-24 13:50:01,871 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-06-24 13:50:01,874 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-06-24 13:50:01,876 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-06-24 18:02:49,026 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
